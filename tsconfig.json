{"extends": "expo/tsconfig.base", "compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "jsx": "react-native", "customConditions": ["react-native"], "module": "esnext", "moduleResolution": "bundler", "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "sourceMap": true, "target": "esnext", "lib": ["esnext", "dom"], "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"src/*": ["./src/*"], "@assets/*": ["./assets/*"]}, "typeRoots": ["./node_modules/@types"]}, "ts-node": {"compilerOptions": {"module": "commonjs"}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}