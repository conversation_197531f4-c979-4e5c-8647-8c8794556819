const palette = {
  neutral100: "#FFFFFF",
  neutral200: "#F4F2F1",
  neutral300: "#D7CEC9",
  neutral400: "#B6ACA6",
  neutral500: "#978F8A",
  neutral600: "#564E4A",
  neutral700: "#3C3836",
  neutral800: "#191015",
  neutral900: "#000000",

  primary50: "#f3f7ff",
  primary100: "#e2ebfe",
  primary200: "#c9dbfd",
  primary300: "#a9c8fc",
  primary400: "#86b5fb",
  primary500: "#60a5fa",
  primary600: "#2181d7",
  primary700: "#1760a2",
  primary800: "#0c4171",
  primary900: "#042340",
  primary950: "#02162c",

  success100: "#D9F4E0",
  success200: "#C7E8C1",
  success300: "#B5D0A8",
  success400: "#A3B790",
  success500: "#42C765",
  success600: "#31A54F",
  success700: "#299C51",
  success800: "#19954A",
  success900: "#127A3E",

  failure100: "#F2D6CD",
  failure200: "#E8B7AD",
  failure300: "#DE998A",
  failure400: "#D27A68",
  failure500: "#C03403",
  failure600: "#A32A02",
  failure700: "#8A2100",
  failure800: "#6D1700",
  failure900: "#4C0D00",

  secondary100: "#DCDDE9",
  secondary200: "#BCC0D6",
  secondary300: "#9196B9",
  secondary400: "#626894",
  secondary500: "#41476E",

  accent100: "#FFEED4",
  accent200: "#FFE1B2",
  accent300: "#FDD495",
  accent400: "#FBC878",
  accent500: "#FFBB50",

  angry100: "#F2D6CD",
  angry500: "#C03403",

  overlay20: "rgba(25, 16, 21, 0.2)",
  overlay50: "rgba(25, 16, 21, 0.5)",
} as const;

export const colors = {
  /**
   * The palette is available to use, but prefer using the name.
   * This is only included for rare, one-off cases. Try to use
   * semantic names as much as possible.
   */
  palette,
  /**
   * A helper for making something see-thru.
   */
  transparent: "rgba(0, 0, 0, 0)",
  /**
   * The default text color in many components.
   */
  text: palette.neutral800,
  /**
   * Secondary text information.
   */
  textDim: palette.neutral600,
  /**
   * The default color of the screen background.
   */
  background: palette.neutral200,
  /**
   * The default border color.
   */
  border: palette.neutral400,
  /**
   * The main tinting color.
   */
  tint: palette.primary500,
  /**
   * The inactive tinting color.
   */
  tintInactive: palette.neutral500,
  /**
   * A subtle color used for lines.
   */
  separator: palette.neutral300,
  /**
   * Error messages.
   */
  error: palette.angry500,
  /**
   * Error Background.
   */
  errorBackground: palette.angry100,
} as const;
