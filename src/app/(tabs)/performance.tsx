import { useFont } from "@shopify/react-native-skia";
import { useQueryClient } from "@tanstack/react-query";
import { useCallback, useMemo, useState } from "react";
import { View } from "react-native";
import { configureReanimatedLogger, ReanimatedLogLevel } from "react-native-reanimated";
import { type SceneRendererProps, TabView } from "reanimated-tab-view";
import { usePlayerSelected } from "src/app/_layout";
import { Card } from "src/components/ui/Card";
import { Screen } from "src/components/ui/Screen";
import { Text } from "src/components/ui/Text";
import { usePlayerStatsMetrics } from "src/hooks/usePlayerStatsMetrics";
import type { Metrics } from "src/services/api/types/player_stats_metrics";
import { useAppTheme } from "src/theme/context";
import { $styles } from "src/theme/styles";
import { titleCase } from "src/utils/strings";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "victory-native";

configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
});

const NAMES_MAP: Record<string, string> = {
  accuracy: "Accuracy",
  assists: "Assists",
  boss_damage: "Boss Dmg",
  boss_damage_per_min: "Boss Dmg/min",
  crit_shot_rate: "Crit Rate",
  deaths: "Deaths",
  denies: "Denies",
  healing: "Healing",
  healing_per_min: "Heal/min",
  kd: "K/D",
  kda: "KDA",
  kills: "Kills",
  kills_plus_assists: "Kills+Assists",
  last_hits: "Last Hits",
  net_worth: "Net Worth",
  net_worth_per_min: "Net/min",
  neutral_damage: "Neutral Dmg",
  neutral_damage_per_min: "Neutral Dmg/min",
  player_damage: "Player Dmg",
  player_damage_per_health: "Dmg/Health",
  player_damage_per_min: "Player Dmg/min",
  player_damage_taken_per_min: "Dmg Taken/min",
  player_healing: "Player Heal",
  player_healing_per_min: "Player Heal/min",
  self_healing: "Self Heal",
  self_healing_per_min: "Self Heal/min",
};

export default function PerformanceScreen() {
  const { theme } = useAppTheme();

  const [player] = usePlayerSelected();
  const [index, setIndex] = useState(0);
  const queryClient = useQueryClient();
  const onRefreshing = useCallback(async () => await queryClient.refetchQueries({ type: "active" }), [queryClient]);

  const now = Math.floor(Date.now() / 1000);
  const nextFullHour = Math.ceil(now / 3600) * 3600;
  const minUnixTimestamp = nextFullHour - 30 * 24 * 60 * 60;
  const { data: communityStatsMetrics, isLoading: isLoadingCommunity } = usePlayerStatsMetrics(minUnixTimestamp);
  const { data: playerStatsMetrics, isLoading: isLoadingPlayer } = usePlayerStatsMetrics(
    minUnixTimestamp,
    player?.account_id,
  );
  const routes = useMemo(
    () =>
      playerStatsMetrics
        ? Object.entries(playerStatsMetrics)
            .map(([key, _]) => ({
              key,
              title: NAMES_MAP[key as string] ?? titleCase(key),
            }))
            .sort(
              (a, b) =>
                Math.abs(playerStatsMetrics[b.key].percentile50 - (communityStatsMetrics?.[b.key].percentile50 ?? 0)) -
                Math.abs(playerStatsMetrics[a.key].percentile50 - (communityStatsMetrics?.[a.key].percentile50 ?? 0)),
            )
        : [],
    [playerStatsMetrics, communityStatsMetrics],
  );

  const display = useCallback(
    (props: SceneRendererProps) => {
      if (isLoadingCommunity || isLoadingPlayer) return <Text text="Loading..." />;
      const communityMetric = communityStatsMetrics?.[props.route.key];
      const playerMetric = playerStatsMetrics?.[props.route.key];
      if (!communityMetric || !playerMetric || !player?.account_id) return <Text text="No data" />;
      return (
        <PlayerVsCommunityDisplay
          accountId={player?.account_id}
          communityMetric={communityMetric}
          playerMetric={playerMetric}
        />
      );
    },
    [isLoadingCommunity, isLoadingPlayer, communityStatsMetrics, playerStatsMetrics, player?.account_id],
  );

  if (!player) {
    return null;
  }

  return (
    <Screen preset="scroll" contentContainerStyle={$styles.containerWithHeader} onRefreshing={onRefreshing}>
      <Card style={{ height: 500 }}>
        <View style={{ flex: 1, gap: theme.spacing.sm }}>
          <Text preset="subheading" text="Player vs Community" style={{ textAlign: "center" }} />
          <TabView
            renderMode="windowed"
            navigationState={{ index, routes }}
            renderScene={display}
            onIndexChange={setIndex}
            swipeEnabled
            tabBarConfig={{
              tabBarScrollEnabled: true,
              tabBarDynamicWidthEnabled: true,
              tabBarStyle: {
                borderRadius: theme.spacing.md,
                backgroundColor: theme.colors.palette.neutral300,
                height: 30,
              },
              tabBarIndicatorStyle: {
                backgroundColor: theme.colors.tint,
                borderRadius: theme.spacing.md,
                height: "100%",
              },
              tabStyle: {
                height: 30,
                paddingHorizontal: theme.spacing.sm,
                paddingVertical: theme.spacing.xxs,
              },
              tabLabelStyle: {
                color: theme.colors.text,
                paddingHorizontal: theme.spacing.xs,
              },
            }}
            keyboardDismissMode="on-drag"
          />
        </View>
      </Card>
    </Screen>
  );
}
export const PlayerVsCommunityDisplay = ({
  communityMetric,
  playerMetric,
}: {
  accountId: number;
  communityMetric: Metrics;
  playerMetric: Metrics;
}) => {
  const { theme } = useAppTheme();
  const font = useFont(require("@assets/fonts/SpaceGrotesk-Medium.ttf"));
  const data = [
    { percentile: 1, community: communityMetric.percentile1, player: playerMetric.percentile1 },
    { percentile: 5, community: communityMetric.percentile5, player: playerMetric.percentile5 },
    { percentile: 10, community: communityMetric.percentile10, player: playerMetric.percentile10 },
    { percentile: 25, community: communityMetric.percentile25, player: playerMetric.percentile25 },
    { percentile: 50, community: communityMetric.percentile50, player: playerMetric.percentile50 },
    { percentile: 75, community: communityMetric.percentile75, player: playerMetric.percentile75 },
    { percentile: 90, community: communityMetric.percentile90, player: playerMetric.percentile90 },
    { percentile: 95, community: communityMetric.percentile95, player: playerMetric.percentile95 },
    { percentile: 99, community: communityMetric.percentile99, player: playerMetric.percentile99 },
  ];
  const minValue = Math.min(...data.map((d) => d.community));
  const maxValue = Math.max(...data.map((d) => d.community));
  return (
    <View style={{ flex: 1, margin: theme.spacing.sm, marginTop: 30 + theme.spacing.md }}>
      <CartesianChart
        data={data}
        xKey="percentile"
        yKeys={["community", "player"]}
        axisOptions={{
          font,
          lineColor: theme.colors.text,
          labelColor: theme.colors.text,
          formatXLabel: (x) => `${x}%`,
          formatYLabel: (y) => {
            if (0 <= minValue && minValue <= maxValue && maxValue <= 1) {
              return `${(100 * y).toFixed(0)}%`;
            }
            if (y >= 1000) {
              return `${(y / 1000).toFixed(y < 10000 ? 1 : 0)}k`;
            }
            return y.toFixed(0);
          },
        }}
      >
        {({ points }) => [
          <Line key="community" points={points.community} color={theme.colors.tint} strokeWidth={3} />,
          <Line key="player" points={points.player} color={theme.colors.palette.failure500} strokeWidth={3} />,
        ]}
      </CartesianChart>
      <Text size="xs" style={{ textAlign: "center" }}>
        Percentile
      </Text>
      <View
        style={{ flexDirection: "row", gap: theme.spacing.sm, marginTop: theme.spacing.xs, marginHorizontal: "auto" }}
      >
        <Text size="xs" style={{ color: theme.colors.tint }}>
          Community
        </Text>
        <Text size="xs" style={{ color: theme.colors.palette.failure500 }}>
          You
        </Text>
      </View>
    </View>
  );
};
