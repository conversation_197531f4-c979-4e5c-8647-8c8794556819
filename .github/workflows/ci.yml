name: CI

on:
  push:

jobs:
  expo-doctor:
    name: <PERSON> Doctor
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Expo Doctor
        run: pnpx expo-doctor

  build:
    name: Build Android
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          lfs: true

      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install dependencies
        run: pnpm install -g eas-cli

      - name: Bundle android
        run: pnpm build:android:sim --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
