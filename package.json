{"name": "deadlock-stats", "version": "0.0.5", "private": true, "main": "index.ts", "license": "GPL-3.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean", "compile": "tsc --noEmit -p . --pretty", "lint": "biome check .", "fmt": "biome format --write .", "align-deps": "npx expo install --fix", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:preview": "eas build --profile preview --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:preview": "eas build --profile preview --platform android --local", "build:android:prod": "eas build --profile production --platform android --local"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@nandorojo/galeria": "^1.2.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-clipboard/clipboard": "^1.16.3", "@shopify/react-native-skia": "2.0.0-next.4", "@tanstack/react-query": "^5.85.5", "apisauce": "3.2.0", "date-fns": "^4.1.0", "expo": "^53.0.22", "expo-application": "~6.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-system-ui": "~5.0.11", "i18next": "^25.4.0", "intl-pluralrules": "^2.0.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.7.1", "react-native": "0.79.5", "react-native-android-widget": "^0.17.1", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-global-state-hooks": "^11.0.1", "react-native-keyboard-controller": "^1.18.5", "react-native-marked": "^7.0.2", "react-native-mmkv": "^3.3.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sse": "^1.2.1", "react-native-svg": "^15.11.2", "react-native-turnstile": "^1.0.9", "react-native-webview": "13.13.5", "reanimated-tab-view": "^0.4.0", "victory-native": "^41.19.3"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "@types/react": "~19.0.14", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "engines": {"node": ">=22.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["intl-pluralrules", "react-native-global-state-hooks", "react-native-turnstile", "services"]}}}}